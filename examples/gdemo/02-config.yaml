backend_clusters:
  - bootstrap_servers:
      - localhost:9092
      - localhost:9093
      - localhost:9094
    name: kafka-localhost
virtual_clusters:
  - name: team-a
    authentication:
      - type: sasl_oauth_bearer
        sasl_oauth_bearer:
          jwks:
            endpoint: http://localhost:8080/realms/kafka-realm/protocol/openid-connect/certs
            refresh: "1s"
        mediation:
          type: anonymous
    backend_cluster_name: kafka-localhost
    route_by:
      port:
        listen_start: 19092
        min_broker_id: 1
      type: port
    topic_rewrite:
      type: prefix
      prefix:
        value: a-      
  - name: team-b
    authentication:
      - mediation:
          type: anonymous
        type: anonymous
    backend_cluster_name: kafka-localhost
    route_by:
      port:
        listen_start: 29092
        min_broker_id: 1
      type: port
    topic_rewrite:
      type: prefix
      prefix:
        value: b-