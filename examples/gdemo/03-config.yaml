backend_clusters:
  - bootstrap_servers:
      - localhost:9092
      - localhost:9093
      - localhost:9094
    name: kafka-localhost
virtual_clusters:
  - name: team-a
    authentication:
      - type: sasl_oauth_bearer
        sasl_oauth_bearer:
          jwks:
            endpoint: http://localhost:8080/realms/kafka-realm/protocol/openid-connect/certs
            refresh: "1s"
        mediation:
          type: anonymous
    backend_cluster_name: kafka-localhost
    route_by:
      port:
        listen_start: 19092
        min_broker_id: 1
      type: port
    topic_rewrite:
      type: prefix
      prefix:
        value: a-      
  - name: team-b
    authentication:
      - mediation:
          type: anonymous
        type: anonymous
    backend_cluster_name: kafka-localhost
    route_by:
      port:
        listen_start: 29092
        min_broker_id: 1
      type: port
    topic_rewrite:
      type: prefix
      prefix:
        value: b-
    consume_policies:
      - policies:
          - type: policy
            policy:
              name: decrypt
              type: decrypt
              spec:
                failure:
                  mode: error
                decrypt:
                  - type: value
                key_sources:
                  - type: static
                    name: inline-key
                    static:
                      - id: "static://key-0"
                        key:
                          type: bytes
                          bytes:
                            value: "byvQt2vqQRc96f4gAJgjNQ=="
    produce_policies:
      - policies:
          - type: policy
            policy:
              name: encrypt 
              type: encrypt
              spec:
                failure: 
                  mode: error
                encrypt:
                  - type: value
                    id: "static://key-0"
                key_sources:
                  - name: inline-key
                    type: static
                    static:
                      - id: "static://key-0"
                        key:
                          type: bytes
                          bytes:
                            value: "byvQt2vqQRc96f4gAJgjNQ=="
