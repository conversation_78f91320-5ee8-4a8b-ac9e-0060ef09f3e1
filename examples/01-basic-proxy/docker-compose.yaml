version: '3'
services:
  kafka:
    image: apache/kafka:3.9.0
    container_name: kafka
    network_mode: host
    healthcheck:
      test: [ "CMD-SHELL", "kafka-topics.sh --bootstrap-server localhost:9092 --list" ]
      interval: 10s
      timeout: 10s
      retries: 5

  knep:
    image: kong/kong-native-event-proxy-dev:main
    container_name: knep
    depends_on:
      - kafka
    network_mode: host
    environment:
    # Running locally, if possible show running from Konnect!!!!
      # KONNECT_CP_HOST: my-konnect-host
      # KONNECT_PAT: my-pat-token
      KNEP__LOCAL_CONFIG_FILE: /var/config/kiburi/config.yaml
      KNEP__OBSERVABILITY__LOG_FLAGS: debug,kiburi=debug,knep=debug
    volumes:
      - ./config.yaml:/var/config/kiburi/config.yaml
